# Build Specifications
## Complete Build Configuration and Deployment Automation

**Agent 23 Deliverable**: Build & Deployment Specialist

### Overview

This document provides comprehensive build specifications for the Mister Smith AI Agent Framework. It defines complete Cargo.toml configurations, build scripts, feature flags, cross-compilation targets, release optimizations, CI/CD pipelines, and development configurations that enable autonomous agents to build and deploy the framework without manual intervention.

---

## 1. Executive Summary

### 1.1 Build System Philosophy

- **Reproducible Builds**: Deterministic builds across all environments
- **Feature Modularity**: Compile only required features to minimize binary size
- **Performance First**: Aggressive optimizations for production deployments
- **Cross-Platform Support**: Native builds for Linux, macOS, Windows, and ARM
- **Automated Pipeline**: Complete CI/CD automation with zero manual steps

### 1.2 Build Architecture

```
┌─────────────────────────────────────────────────────────────┐
│                      BUILD PIPELINE                         │
├─────────────────────────────────────────────────────────────┤
│ Source → Feature Selection → Compilation → Optimization     │
├─────────────────────────────────────────────────────────────┤
│ Testing → Packaging → Signing → Distribution               │
└─────────────────────────────────────────────────────────────┘
```

---

## 2. Complete Cargo.toml Configuration

### 2.1 Root Package Configuration

```toml
[package]
name = "mister-smith-framework"
version = "0.1.0"
edition = "2021"
rust-version = "1.75"
authors = ["Mister Smith AI Framework Team"]
description = "AI Agent Framework with Tokio-based async architecture, supervision trees, and tool integration"
license = "MIT OR Apache-2.0"
repository = "https://github.com/mister-smith/framework"
documentation = "https://docs.rs/mister-smith-framework"
keywords = ["ai", "agents", "async", "tokio", "supervision"]
categories = ["asynchronous", "development-tools"]
readme = "README.md"
homepage = "https://mistersmith.ai"
build = "build.rs"
include = [
    "src/**/*",
    "Cargo.toml",
    "LICENSE-MIT",
    "LICENSE-APACHE",
    "README.md",
    "build.rs",
]
exclude = [
    "target/*",
    "benches/*",
    "tests/*",
    ".github/*",
    "docs/*",
]

[package.metadata]
# Minimum supported Rust version policy
msrv = "1.75.0"
# Security audit configuration
audit = { db-update-frequency = "daily", severity-threshold = "medium" }
# Documentation configuration
docs-rs = { all-features = true, rustdoc-args = ["--cfg", "docsrs"] }

[badges]
maintenance = { status = "actively-developed" }
```

### 2.2 Feature Flag Architecture

```toml
[features]
# === DEFAULT FEATURES ===
# Core functionality enabled by default
default = ["runtime", "actors", "tools", "monitoring", "config"]

# === TIER-BASED FEATURE SETS ===
# Tier 1: Experimental/Development features
tier_1 = ["default"]

# Tier 2: Validation/Staging features
tier_2 = [
    "default",
    "security",
    "persistence",
    "http-client",
    "metrics"
]

# Tier 3: Production features
tier_3 = [
    "tier_2",
    "encryption",
    "clustering",
    "tracing",
    "compression",
    "performance"
]

# === CORE SYSTEM FEATURES ===
runtime = ["dep:tokio", "tokio/full"]
actors = ["dep:async-trait", "dep:crossbeam-channel"]
tools = ["dep:serde_json", "dep:jsonschema"]
monitoring = ["dep:prometheus", "dep:metrics"]
supervision = ["dep:crossbeam-utils", "dep:atomic_float"]
config = ["dep:config", "dep:notify", "dep:toml"]

# === SECURITY FEATURES ===
security = ["dep:ring", "dep:jwt-simple", "dep:zeroize"]
encryption = ["security", "dep:aes-gcm", "dep:chacha20poly1305"]
auth = ["security", "dep:oauth2", "dep:jsonwebtoken"]
mtls = ["security", "dep:rustls", "dep:rustls-pemfile"]

# === PERSISTENCE FEATURES ===
persistence = []
sql = ["persistence", "dep:sqlx", "sqlx/runtime-tokio-rustls"]
postgres = ["sql", "sqlx/postgres"]
sqlite = ["sql", "sqlx/sqlite"]
mysql = ["sql", "sqlx/mysql"]
redis = ["persistence", "dep:redis", "redis/tokio-comp"]
embedded-db = ["persistence", "dep:sled"]

# === DISTRIBUTED SYSTEM FEATURES ===
clustering = ["dep:raft", "dep:async-nats"]
consensus = ["clustering", "raft/prost-codec"]
messaging = ["clustering", "async-nats/jetstream"]
service-discovery = ["clustering", "dep:consul"]

# === OBSERVABILITY FEATURES ===
metrics = ["monitoring", "dep:metrics", "dep:metrics-exporter-prometheus"]
tracing = [
    "dep:tracing",
    "dep:tracing-subscriber",
    "dep:tracing-opentelemetry",
    "dep:opentelemetry"
]
structured-logging = ["tracing", "tracing-subscriber/json"]
health-checks = ["monitoring", "dep:tower", "dep:tower-http"]

# === PERFORMANCE FEATURES ===
performance = ["simd", "parallel", "jemalloc"]
simd = ["dep:wide"]
parallel = ["dep:rayon"]
compression = ["dep:lz4", "dep:zstd"]
jemalloc = ["dep:tikv-jemallocator"]

# === NETWORK FEATURES ===
http-client = ["dep:reqwest", "reqwest/json", "reqwest/stream"]
websockets = ["http-client", "dep:tokio-tungstenite"]
grpc = ["dep:tonic", "dep:prost"]

# === CLAUDE CLI INTEGRATION ===
claude-cli = [
    "dep:async-process",
    "dep:signal-hook",
    "messaging",
    "dep:nom"
]

# === DEVELOPMENT FEATURES ===
dev = ["testing", "mocking", "fixtures"]
testing = ["dep:mockall", "dep:wiremock", "dep:proptest"]
mocking = ["testing", "mockall/unstable"]
fixtures = ["testing", "dep:rstest"]
profiling = ["dep:pprof", "dep:dhat"]
unstable = []
```

### 2.3 Dependencies (Building on Agent 18's Work)

```toml
[dependencies]
# === ASYNC RUNTIME ===
tokio = { version = "1.45.0", optional = true }
futures = "0.3.31"
async-trait = { version = "0.1.83", optional = true }
pin-project = "1.1.6"

# === SERIALIZATION ===
serde = { version = "1.0.214", features = ["derive"] }
serde_json = { version = "1.0.132", optional = true }
toml = { version = "0.8.19", optional = true }
jsonschema = { version = "0.18.3", optional = true }

# === ERROR HANDLING ===
thiserror = "1.0.69"
anyhow = "1.0.93"

# === LOGGING AND TRACING ===
tracing = { version = "0.1.41", optional = true }
tracing-subscriber = { version = "0.3.18", optional = true }
tracing-opentelemetry = { version = "0.26.0", optional = true }
opentelemetry = { version = "0.26.0", optional = true }

# === COLLECTIONS ===
indexmap = "2.6.0"
dashmap = "6.1.0"
smallvec = "1.13.2"

# === CONCURRENCY ===
crossbeam-channel = { version = "0.5.13", optional = true }
crossbeam-utils = { version = "0.8.20", optional = true }
atomic_float = { version = "1.1.0", optional = true }
parking_lot = "0.12.3"

# === TIME ===
chrono = { version = "0.4.38", features = ["serde"] }
cron = "0.12.1"

# === SECURITY ===
ring = { version = "0.17.8", optional = true }
jwt-simple = { version = "0.12.10", optional = true }
aes-gcm = { version = "0.10.3", optional = true }
chacha20poly1305 = { version = "0.10.1", optional = true }
zeroize = { version = "1.8.1", optional = true }
rustls = { version = "0.23.18", optional = true }
rustls-pemfile = { version = "2.2.0", optional = true }

# === DATABASE ===
sqlx = { version = "0.8.2", optional = true, default-features = false }
redis = { version = "0.27.5", optional = true, default-features = false }
sled = { version = "0.34.7", optional = true }

# === DISTRIBUTED SYSTEMS ===
raft = { version = "0.7.0", optional = true }
async-nats = { version = "0.37.0", optional = true }
consul = { version = "0.4.2", optional = true }

# === HTTP/NETWORKING ===
reqwest = { version = "0.12.9", optional = true, default-features = false }
tokio-tungstenite = { version = "0.24.0", optional = true }
tonic = { version = "0.12.3", optional = true }
prost = { version = "0.13.3", optional = true }

# === METRICS ===
prometheus = { version = "0.13.4", optional = true }
metrics = { version = "0.23.0", optional = true }
metrics-exporter-prometheus = { version = "0.15.3", optional = true }

# === PERFORMANCE ===
wide = { version = "0.7.28", optional = true }
rayon = { version = "1.10.0", optional = true }
lz4 = { version = "1.28.0", optional = true }
zstd = { version = "0.13.2", optional = true }
tikv-jemallocator = { version = "0.6.0", optional = true }

# === CLAUDE CLI ===
async-process = { version = "2.3.0", optional = true }
signal-hook = { version = "0.3.17", optional = true }
nom = { version = "7.1.3", optional = true }

# === CONFIGURATION ===
config = { version = "0.14.1", optional = true }
notify = { version = "6.1.1", optional = true }

# === UTILITIES ===
uuid = { version = "1.11.0", features = ["v4", "serde"] }
once_cell = "1.20.2"
url = "2.5.4"
dirs = "5.0.1"

# === TOWER MIDDLEWARE ===
tower = { version = "0.5.1", optional = true }
tower-http = { version = "0.6.2", optional = true }

[dev-dependencies]
# Testing framework
tokio-test = "0.4.4"
mockall = { version = "0.13.0", optional = true }
wiremock = { version = "0.6.2", optional = true }
proptest = { version = "1.5.0", optional = true }
rstest = { version = "0.23.0", optional = true }

# Benchmarking
criterion = { version = "0.5.1", features = ["html_reports"] }

# Profiling
pprof = { version = "0.13.0", optional = true, features = ["criterion"] }
dhat = { version = "0.3.3", optional = true }

# Test utilities
tempfile = "3.14.0"
env_logger = "0.11.5"
serial_test = "3.1.1"

[build-dependencies]
# Build script dependencies
vergen = { version = "9.0.1", features = ["build", "git", "gitcl", "cargo"] }
cc = "1.2.2"
prost-build = { version = "0.13.3", optional = true }
```

---

## 3. Build Script Specifications

### 3.1 Main Build Script (build.rs)

```rust
// build.rs - Main build script for code generation and configuration
use std::env;
use std::path::PathBuf;
use vergen::{BuildBuilder, CargoBuilder, GitBuilder, RustcBuilder};

fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Set up build-time code generation
    generate_build_info()?;
    generate_feature_matrix()?;
    
    // Conditional compilation based on features
    #[cfg(feature = "grpc")]
    compile_proto_files()?;
    
    #[cfg(feature = "claude-cli")]
    generate_claude_cli_hooks()?;
    
    // Platform-specific optimizations
    configure_platform_optimizations()?;
    
    // Asset bundling
    bundle_configuration_templates()?;
    
    Ok(())
}

fn generate_build_info() -> Result<(), Box<dyn std::error::Error>> {
    // Generate build information using vergen
    let build = BuildBuilder::all_build()?;
    let cargo = CargoBuilder::all_cargo()?;
    let git = GitBuilder::all_git()?;
    let rustc = RustcBuilder::all_rustc()?;
    
    // Emit build-time environment variables
    vergen::Emitter::default()
        .add_instructions(&build)?
        .add_instructions(&cargo)?
        .add_instructions(&git)?
        .add_instructions(&rustc)?
        .emit()?;
    
    Ok(())
}

fn generate_feature_matrix() -> Result<(), Box<dyn std::error::Error>> {
    // Generate feature compatibility matrix
    let out_dir = PathBuf::from(env::var("OUT_DIR")?);
    let feature_matrix = generate_feature_compatibility_matrix();
    
    std::fs::write(
        out_dir.join("feature_matrix.rs"),
        feature_matrix
    )?;
    
    Ok(())
}

#[cfg(feature = "grpc")]
fn compile_proto_files() -> Result<(), Box<dyn std::error::Error>> {
    // Compile protocol buffer definitions
    prost_build::Config::new()
        .service_generator(tonic_build::service_generator())
        .compile_protos(
            &[
                "proto/agent.proto",
                "proto/coordination.proto",
                "proto/messaging.proto",
            ],
            &["proto"],
        )?;
    
    Ok(())
}

#[cfg(feature = "claude-cli")]
fn generate_claude_cli_hooks() -> Result<(), Box<dyn std::error::Error>> {
    // Generate Claude CLI hook definitions
    let hooks = include_str!("claude-hooks.toml");
    let generated = generate_hook_code(hooks)?;
    
    let out_dir = PathBuf::from(env::var("OUT_DIR")?);
    std::fs::write(out_dir.join("claude_hooks.rs"), generated)?;
    
    Ok(())
}

fn configure_platform_optimizations() -> Result<(), Box<dyn std::error::Error>> {
    let target = env::var("TARGET")?;
    
    match target.as_str() {
        t if t.contains("x86_64") => {
            // Enable AVX2/AVX512 if available
            if is_x86_feature_available("avx2") {
                println!("cargo:rustc-cfg=feature=\"simd_avx2\"");
            }
        }
        t if t.contains("aarch64") => {
            // Enable NEON optimizations
            println!("cargo:rustc-cfg=feature=\"simd_neon\"");
        }
        t if t.contains("wasm32") => {
            // WebAssembly optimizations
            println!("cargo:rustc-cfg=feature=\"wasm_simd128\"");
        }
        _ => {}
    }
    
    Ok(())
}

fn bundle_configuration_templates() -> Result<(), Box<dyn std::error::Error>> {
    // Bundle default configuration templates
    let config_dir = PathBuf::from("config-templates");
    let out_dir = PathBuf::from(env::var("OUT_DIR")?);
    
    let configs = std::fs::read_dir(config_dir)?
        .filter_map(|entry| entry.ok())
        .filter(|entry| entry.path().extension() == Some("toml".as_ref()))
        .collect::<Vec<_>>();
    
    // Generate embedded configuration module
    let mut config_module = String::new();
    config_module.push_str("pub mod embedded_configs {\n");
    
    for config in configs {
        let name = config.file_name().to_string_lossy().replace(".toml", "");
        let content = std::fs::read_to_string(config.path())?;
        
        config_module.push_str(&format!(
            "    pub const {}: &str = r#\"{}\"#;\n",
            name.to_uppercase(),
            content
        ));
    }
    
    config_module.push_str("}\n");
    
    std::fs::write(out_dir.join("embedded_configs.rs"), config_module)?;
    
    Ok(())
}

// Helper functions
fn is_x86_feature_available(feature: &str) -> bool {
    std::process::Command::new("sh")
        .arg("-c")
        .arg(format!("grep {} /proc/cpuinfo", feature))
        .status()
        .map(|status| status.success())
        .unwrap_or(false)
}

fn generate_feature_compatibility_matrix() -> String {
    // Generate compile-time feature compatibility checks
    r#"
/// Feature compatibility matrix generated at build time
pub mod feature_compat {
    #[cfg(all(feature = "encryption", not(feature = "security")))]
    compile_error!("encryption feature requires security feature");
    
    #[cfg(all(feature = "clustering", not(feature = "messaging")))]
    compile_error!("clustering feature requires messaging feature");
    
    #[cfg(all(feature = "tracing", not(feature = "monitoring")))]
    compile_error!("tracing feature requires monitoring feature");
    
    pub const fn validate_features() -> bool {
        true
    }
}
"#.to_string()
}

fn generate_hook_code(hooks_toml: &str) -> Result<String, Box<dyn std::error::Error>> {
    // Parse hook definitions and generate code
    // This would parse the TOML and generate appropriate hook registration code
    Ok(format!(
        r#"
/// Generated Claude CLI hook definitions
pub mod claude_hooks {{
    use crate::hooks::{{HookRegistry, Hook}};
    
    pub fn register_hooks(registry: &mut HookRegistry) {{
        // Generated hook registrations
        {}
    }}
}}
"#,
        "// Hook registrations would go here"
    ))
}
```

### 3.2 Platform-Specific Build Scripts

```rust
// build-linux.rs - Linux-specific build configuration
#[cfg(target_os = "linux")]
fn configure_linux_build() -> Result<(), Box<dyn std::error::Error>> {
    // Link with system libraries
    println!("cargo:rustc-link-lib=systemd");
    
    // Enable io_uring if available
    if pkg_config::probe_library("liburing").is_ok() {
        println!("cargo:rustc-cfg=feature=\"io_uring\"");
    }
    
    // Configure seccomp filters
    if cfg!(feature = "security") {
        println!("cargo:rustc-link-lib=seccomp");
    }
    
    Ok(())
}

// build-macos.rs - macOS-specific build configuration
#[cfg(target_os = "macos")]
fn configure_macos_build() -> Result<(), Box<dyn std::error::Error>> {
    // Link with system frameworks
    println!("cargo:rustc-link-lib=framework=Security");
    println!("cargo:rustc-link-lib=framework=CoreFoundation");
    
    // Enable Grand Central Dispatch integration
    println!("cargo:rustc-cfg=feature=\"gcd_integration\"");
    
    Ok(())
}

// build-windows.rs - Windows-specific build configuration
#[cfg(target_os = "windows")]
fn configure_windows_build() -> Result<(), Box<dyn std::error::Error>> {
    // Link with Windows libraries
    println!("cargo:rustc-link-lib=ws2_32");
    println!("cargo:rustc-link-lib=userenv");
    
    // Enable Windows-specific features
    println!("cargo:rustc-cfg=feature=\"windows_service\"");
    
    Ok(())
}
```

---

## 4. Cross-Compilation Targets

### 4.1 Supported Target Matrix

```toml
# .cargo/config.toml - Cross-compilation configuration

[target.x86_64-unknown-linux-gnu]
linker = "x86_64-linux-gnu-gcc"
rustflags = ["-C", "target-cpu=x86-64-v2"]

[target.x86_64-unknown-linux-musl]
linker = "musl-gcc"
rustflags = ["-C", "target-feature=+crt-static"]

[target.aarch64-unknown-linux-gnu]
linker = "aarch64-linux-gnu-gcc"
rustflags = ["-C", "target-cpu=cortex-a72"]

[target.aarch64-apple-darwin]
rustflags = ["-C", "target-cpu=apple-m1"]

[target.x86_64-pc-windows-msvc]
rustflags = ["-C", "target-feature=+crt-static"]

[target.wasm32-unknown-unknown]
rustflags = ["-C", "opt-level=z", "-C", "lto=fat"]

[target.wasm32-wasi]
runner = "wasmtime"
rustflags = ["-C", "opt-level=z"]

# Cross-compilation aliases
[alias]
build-linux = "build --target x86_64-unknown-linux-gnu"
build-linux-arm = "build --target aarch64-unknown-linux-gnu"
build-macos = "build --target x86_64-apple-darwin"
build-macos-arm = "build --target aarch64-apple-darwin"
build-windows = "build --target x86_64-pc-windows-msvc"
build-wasm = "build --target wasm32-unknown-unknown"
build-all-targets = """
    build --target x86_64-unknown-linux-gnu &&
    build --target aarch64-unknown-linux-gnu &&
    build --target x86_64-apple-darwin &&
    build --target aarch64-apple-darwin &&
    build --target x86_64-pc-windows-msvc
"""
```

### 4.2 Cross-Compilation Docker Configuration

```dockerfile
# Dockerfile.cross - Multi-arch build environment
FROM rust:1.75 as builder

# Install cross-compilation toolchains
RUN apt-get update && apt-get install -y \
    gcc-x86-64-linux-gnu \
    gcc-aarch64-linux-gnu \
    gcc-arm-linux-gnueabihf \
    mingw-w64 \
    musl-tools \
    libssl-dev \
    pkg-config \
    && rm -rf /var/lib/apt/lists/*

# Install cross compilation tool
RUN cargo install cross --version 0.2.5

# Set up Rust targets
RUN rustup target add \
    x86_64-unknown-linux-gnu \
    x86_64-unknown-linux-musl \
    aarch64-unknown-linux-gnu \
    aarch64-unknown-linux-musl \
    armv7-unknown-linux-gnueabihf \
    x86_64-pc-windows-gnu \
    wasm32-unknown-unknown \
    wasm32-wasi

# Create build script
COPY <<'EOF' /usr/local/bin/build-all-targets.sh
#!/bin/bash
set -e

TARGETS=(
    "x86_64-unknown-linux-gnu"
    "x86_64-unknown-linux-musl"
    "aarch64-unknown-linux-gnu"
    "aarch64-unknown-linux-musl"
    "x86_64-pc-windows-gnu"
)

for target in "${TARGETS[@]}"; do
    echo "Building for $target..."
    cross build --release --target "$target" --features "tier_3"
    
    # Create output directory
    mkdir -p "dist/$target"
    
    # Copy binaries
    cp "target/$target/release/mister-smith-framework" "dist/$target/" 2>/dev/null || \
    cp "target/$target/release/mister-smith-framework.exe" "dist/$target/" 2>/dev/null || true
done

# Build WASM targets
echo "Building WASM targets..."
cargo build --release --target wasm32-unknown-unknown --features "wasm"
cargo build --release --target wasm32-wasi --features "wasm"

mkdir -p dist/wasm
cp target/wasm32-*/release/*.wasm dist/wasm/
EOF

RUN chmod +x /usr/local/bin/build-all-targets.sh

WORKDIR /app
ENTRYPOINT ["/usr/local/bin/build-all-targets.sh"]
```

---

## 5. Release Build Profiles

### 5.1 Optimization Profiles

```toml
# Cargo.toml - Build profiles

[profile.dev]
opt-level = 0
debug = 2
debug-assertions = true
overflow-checks = true
lto = false
panic = 'unwind'
incremental = true
codegen-units = 256
rpath = false

[profile.dev.package."*"]
# Optimize dependencies even in dev mode
opt-level = 2

[profile.release]
opt-level = 3
debug = false
debug-assertions = false
overflow-checks = false
lto = "fat"
panic = 'abort'
incremental = false
codegen-units = 1
rpath = false
strip = true

[profile.release-with-debug]
inherits = "release"
debug = true
strip = false

[profile.bench]
inherits = "release"
debug = true
lto = true
codegen-units = 1

[profile.minimal]
inherits = "release"
opt-level = "z"     # Optimize for size
lto = "fat"
codegen-units = 1
strip = true
panic = "abort"

[profile.maxperf]
inherits = "release"
lto = "fat"
codegen-units = 1
opt-level = 3
# Enable target-specific CPU features
rustflags = ["-C", "target-cpu=native"]
```

### 5.2 Build Optimization Strategies

```toml
# .cargo/config.toml - Build optimizations

[build]
# Use all available cores for compilation
jobs = "auto"
# Enable incremental compilation for development
incremental = true
# Use mold linker for faster linking (Linux)
target-dir = "target"

[target.'cfg(all(target_os = "linux", target_arch = "x86_64"))']
linker = "clang"
rustflags = [
    "-C", "link-arg=-fuse-ld=mold",
    "-C", "target-cpu=x86-64-v3",
    "-Z", "share-generics=y",
]

[target.'cfg(target_os = "macos")']
rustflags = [
    "-C", "target-cpu=native",
    "-C", "link-arg=-undefined",
    "-C", "link-arg=dynamic_lookup",
]

[target.'cfg(target_os = "windows")']
rustflags = [
    "-C", "target-cpu=x86-64-v3",
    "-C", "link-arg=/LTCG",
    "-C", "link-arg=/OPT:REF",
    "-C", "link-arg=/OPT:ICF",
]

# Profile-guided optimization configuration
[profile.release.build-override]
opt-level = 3
codegen-units = 1

[profile.release.package."*"]
opt-level = 3
codegen-units = 1
```

### 5.3 Binary Size Optimization

```bash
#!/bin/bash
# scripts/optimize-binary.sh - Post-build binary optimization

set -euo pipefail

BINARY=$1
OUTPUT=$2

echo "Optimizing binary: $BINARY"

# Strip symbols
strip --strip-all "$BINARY"

# Compress with UPX if available
if command -v upx &> /dev/null; then
    upx --best --lzma "$BINARY" -o "$OUTPUT.upx"
fi

# Generate size report
size "$BINARY" > "$OUTPUT.size"

# Analyze binary bloat
if command -v cargo-bloat &> /dev/null; then
    cargo bloat --release --crates > "$OUTPUT.bloat"
fi

echo "Optimization complete. Results:"
ls -lh "$BINARY" "$OUTPUT"*
```

---

## 6. CI/CD Pipeline Specifications

### 6.1 GitHub Actions Workflow

```yaml
# .github/workflows/build-and-release.yml
name: Build and Release

on:
  push:
    branches: [main, develop]
    tags: ['v*']
  pull_request:
    branches: [main]
  schedule:
    - cron: '0 0 * * 1'  # Weekly builds

env:
  RUST_BACKTRACE: 1
  CARGO_TERM_COLOR: always
  RUSTFLAGS: "-D warnings"

jobs:
  # === DEPENDENCY AUDIT ===
  security-audit:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: actions-rs/audit-check@v1
        with:
          token: ${{ secrets.GITHUB_TOKEN }}

  # === LINT AND FORMAT ===
  lint:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4
      - uses: dtolnay/rust-toolchain@stable
        with:
          components: rustfmt, clippy
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
      
      - name: Check formatting
        run: cargo fmt -- --check
      
      - name: Clippy analysis
        run: cargo clippy --all-targets --all-features -- -D warnings

  # === BUILD MATRIX ===
  build:
    needs: [security-audit, lint]
    strategy:
      fail-fast: false
      matrix:
        include:
          # Linux builds
          - target: x86_64-unknown-linux-gnu
            os: ubuntu-latest
            features: tier_3
            
          - target: x86_64-unknown-linux-musl
            os: ubuntu-latest
            features: tier_3
            use-cross: true
            
          - target: aarch64-unknown-linux-gnu
            os: ubuntu-latest
            features: tier_3
            use-cross: true
            
          # macOS builds
          - target: x86_64-apple-darwin
            os: macos-latest
            features: tier_3
            
          - target: aarch64-apple-darwin
            os: macos-latest
            features: tier_3
            
          # Windows builds
          - target: x86_64-pc-windows-msvc
            os: windows-latest
            features: tier_3
            
          # WASM builds
          - target: wasm32-unknown-unknown
            os: ubuntu-latest
            features: wasm
            
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Install Rust toolchain
        uses: dtolnay/rust-toolchain@stable
        with:
          targets: ${{ matrix.target }}
      
      - name: Install cross
        if: matrix.use-cross
        run: cargo install cross --version 0.2.5
      
      - name: Cache dependencies
        uses: Swatinem/rust-cache@v2
        with:
          key: ${{ matrix.target }}
      
      - name: Build
        run: |
          if [[ "${{ matrix.use-cross }}" == "true" ]]; then
            cross build --release --target ${{ matrix.target }} --features ${{ matrix.features }}
          else
            cargo build --release --target ${{ matrix.target }} --features ${{ matrix.features }}
          fi
      
      - name: Run tests
        if: matrix.target == 'x86_64-unknown-linux-gnu'
        run: cargo test --release --features ${{ matrix.features }}
      
      - name: Package artifacts
        run: |
          mkdir -p dist/${{ matrix.target }}
          
          # Copy binary
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            cp target/${{ matrix.target }}/release/*.exe dist/${{ matrix.target }}/ || true
          else
            cp target/${{ matrix.target }}/release/mister-smith-framework dist/${{ matrix.target }}/ || true
          fi
          
          # Copy additional files
          cp README.md LICENSE-MIT LICENSE-APACHE dist/${{ matrix.target }}/
          
          # Create archive
          cd dist
          if [[ "${{ matrix.os }}" == "windows-latest" ]]; then
            7z a mister-smith-${{ matrix.target }}.zip ${{ matrix.target }}/*
          else
            tar czf mister-smith-${{ matrix.target }}.tar.gz ${{ matrix.target }}/*
          fi
      
      - name: Upload artifacts
        uses: actions/upload-artifact@v4
        with:
          name: mister-smith-${{ matrix.target }}
          path: dist/mister-smith-${{ matrix.target }}.*

  # === RELEASE ===
  release:
    needs: build
    if: startsWith(github.ref, 'refs/tags/')
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Download all artifacts
        uses: actions/download-artifact@v4
        with:
          path: artifacts
      
      - name: Create release
        uses: softprops/action-gh-release@v2
        with:
          files: artifacts/**/*
          draft: false
          prerelease: false
          generate_release_notes: true
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  # === DOCKER BUILD ===
  docker:
    needs: build
    runs-on: ubuntu-latest
    
    steps:
      - uses: actions/checkout@v4
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Login to DockerHub
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKER_USERNAME }}
          password: ${{ secrets.DOCKER_TOKEN }}
      
      - name: Build and push
        uses: docker/build-push-action@v5
        with:
          context: .
          platforms: linux/amd64,linux/arm64
          push: true
          tags: |
            mistersmith/framework:latest
            mistersmith/framework:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
```

### 6.2 Build Caching Strategy

```yaml
# .github/workflows/cache-warming.yml
name: Cache Warming

on:
  schedule:
    - cron: '0 */6 * * *'  # Every 6 hours
  workflow_dispatch:

jobs:
  warm-cache:
    strategy:
      matrix:
        os: [ubuntu-latest, macos-latest, windows-latest]
        rust: [stable, nightly]
    
    runs-on: ${{ matrix.os }}
    
    steps:
      - uses: actions/checkout@v4
      
      - uses: dtolnay/rust-toolchain@master
        with:
          toolchain: ${{ matrix.rust }}
      
      - uses: Swatinem/rust-cache@v2
        with:
          cache-on-failure: true
      
      - name: Build dependencies only
        run: |
          cargo build --features full
          cargo build --features tier_1
          cargo build --features tier_2
          cargo build --features tier_3
```

---

## 7. Development Build Configuration

### 7.1 Development Environment Setup

```bash
#!/bin/bash
# scripts/dev-setup.sh - Development environment setup

set -euo pipefail

echo "Setting up Mister Smith development environment..."

# Install Rust toolchain
if ! command -v rustc &> /dev/null; then
    echo "Installing Rust..."
    curl --proto '=https' --tlsv1.2 -sSf https://sh.rustup.rs | sh -s -- -y
    source "$HOME/.cargo/env"
fi

# Install required Rust version
rustup toolchain install 1.75
rustup default 1.75

# Add necessary components
rustup component add rustfmt clippy rust-src rust-analyzer

# Install cargo extensions
cargo install --locked \
    cargo-watch \
    cargo-expand \
    cargo-audit \
    cargo-outdated \
    cargo-machete \
    cargo-deny \
    cargo-nextest \
    cargo-llvm-lines \
    cargo-bloat

# Platform-specific setup
case "$(uname -s)" in
    Linux*)
        # Install mold linker for faster builds
        if ! command -v mold &> /dev/null; then
            echo "Installing mold linker..."
            curl -LO https://github.com/rui314/mold/releases/latest/download/mold-linux-x86_64.tar.gz
            tar xzf mold-linux-x86_64.tar.gz
            sudo cp mold-*/bin/mold /usr/local/bin/
            sudo cp mold-*/lib/mold/mold-wrapper.so /usr/local/lib/
            rm -rf mold-*
        fi
        ;;
    Darwin*)
        # Install development tools via Homebrew
        if command -v brew &> /dev/null; then
            brew install pkg-config openssl
        fi
        ;;
    MINGW*|MSYS*|CYGWIN*)
        echo "Windows detected. Please ensure Visual Studio Build Tools are installed."
        ;;
esac

# Set up git hooks
if [ -d .git ]; then
    echo "Installing git hooks..."
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
set -e
cargo fmt -- --check
cargo clippy --all-targets --all-features -- -D warnings
cargo test --workspace
EOF
    chmod +x .git/hooks/pre-commit
fi

# Create development configuration
mkdir -p .cargo
cat > .cargo/config.toml << 'EOF'
[build]
# Faster linking
rustflags = ["-C", "link-arg=-fuse-ld=mold"]

[alias]
# Development shortcuts
d = "doc --open"
c = "check --all-features"
t = "test --all-features"
b = "build --all-features"
w = "watch -x check -x test"
EOF

echo "Development environment setup complete!"
```

### 7.2 Development Workflows

```makefile
# Makefile - Development workflow automation

.PHONY: all build test check clean doc bench

# Default target
all: check test build

# Development build
dev:
	cargo build --features dev

# Production build
build:
	cargo build --release --features tier_3

# Run all tests
test:
	cargo nextest run --all-features
	cargo test --doc --all-features

# Run checks
check:
	cargo fmt -- --check
	cargo clippy --all-targets --all-features -- -D warnings
	cargo audit
	cargo outdated

# Clean build artifacts
clean:
	cargo clean
	rm -rf dist/

# Generate documentation
doc:
	cargo doc --all-features --no-deps --open

# Run benchmarks
bench:
	cargo bench --all-features

# Watch for changes
watch:
	cargo watch -x check -x test

# Run with profiling
profile:
	cargo build --release --features "tier_3 profiling"
	CARGO_PROFILE_RELEASE_DEBUG=true cargo run --release

# Security audit
security:
	cargo audit --db-update
	cargo deny check

# Update dependencies
update:
	cargo update
	cargo outdated

# Cross-compilation targets
build-linux:
	cross build --release --target x86_64-unknown-linux-gnu --features tier_3

build-macos:
	cargo build --release --target x86_64-apple-darwin --features tier_3

build-windows:
	cross build --release --target x86_64-pc-windows-gnu --features tier_3

build-all: build-linux build-macos build-windows

# Docker builds
docker:
	docker build -t mister-smith-framework:latest .

docker-cross:
	docker build -f Dockerfile.cross -t mister-smith-cross:latest .
	docker run --rm -v $(PWD):/app mister-smith-cross:latest

# Release preparation
release-prep:
	cargo test --all-features
	cargo audit
	cargo build --release --features tier_3
	./scripts/optimize-binary.sh target/release/mister-smith-framework dist/
```

### 7.3 Development Container Configuration

```dockerfile
# .devcontainer/Dockerfile
FROM rust:1.75

# Install development tools
RUN apt-get update && apt-get install -y \
    build-essential \
    pkg-config \
    libssl-dev \
    git \
    vim \
    tmux \
    htop \
    valgrind \
    gdb \
    && rm -rf /var/lib/apt/lists/*

# Install Rust development tools
RUN rustup component add rustfmt clippy rust-analyzer
RUN cargo install cargo-watch cargo-expand cargo-edit

# Install mold linker
RUN curl -LO https://github.com/rui314/mold/releases/latest/download/mold-linux-x86_64.tar.gz && \
    tar xzf mold-linux-x86_64.tar.gz && \
    cp mold-*/bin/mold /usr/local/bin/ && \
    cp mold-*/lib/mold/mold-wrapper.so /usr/local/lib/ && \
    rm -rf mold-*

# Set up development environment
ENV CARGO_TARGET_DIR=/tmp/target
ENV RUSTFLAGS="-C link-arg=-fuse-ld=mold"

WORKDIR /workspace
```

---

## 8. Performance Build Optimizations

### 8.1 CPU-Specific Optimizations

```toml
# cpu-features.toml - CPU feature detection and optimization

[optimizations.x86_64]
# Intel/AMD x86_64 optimizations
baseline = ["sse2"]
v2 = ["sse3", "ssse3", "sse4.1", "sse4.2", "popcnt"]
v3 = ["avx", "avx2", "bmi1", "bmi2", "f16c", "fma", "lzcnt", "movbe", "xsave"]
v4 = ["avx512f", "avx512bw", "avx512cd", "avx512dq", "avx512vl"]

[optimizations.aarch64]
# ARM64 optimizations
baseline = ["neon"]
v8_1 = ["lse", "rdm"]
v8_2 = ["dotprod", "fp16"]
v8_3 = ["pauth", "rcpc"]

[build-flags]
# Optimization flags per architecture
x86_64 = [
    "-C", "target-cpu=x86-64-v3",
    "-C", "target-feature=+aes,+rdrand,+rdseed"
]

aarch64 = [
    "-C", "target-cpu=cortex-a72",
    "-C", "target-feature=+neon,+fp16,+aes"
]
```

### 8.2 Link-Time Optimization Configuration

```rust
// src/build/lto.rs - LTO configuration helper

pub fn configure_lto() -> &'static str {
    match (cfg!(debug_assertions), cfg!(feature = "dev")) {
        (true, _) => "off",      // No LTO in debug mode
        (false, true) => "thin",  // Thin LTO for dev releases
        (false, false) => "fat",  // Full LTO for production
    }
}

pub fn configure_codegen_units() -> usize {
    match (cfg!(debug_assertions), num_cpus::get()) {
        (true, cpus) => cpus * 2,  // More parallelism in debug
        (false, _) => 1,            // Single unit for maximum optimization
    }
}
```

---

## 9. Build Caching and Optimization

### 9.1 Incremental Compilation Cache

```toml
# .cargo/config.toml - Cache configuration

[build]
# Incremental compilation settings
incremental = true
target-dir = "target"

# Cargo cache settings
[env]
CARGO_HOME = ".cargo"
CARGO_TARGET_DIR = "target"
CARGO_INCREMENTAL = "1"

# sccache configuration for distributed builds
RUSTC_WRAPPER = "sccache"
SCCACHE_CACHE_SIZE = "10G"
SCCACHE_ERROR_LOG = "/tmp/sccache_error.log"
```

### 9.2 Build Cache Management

```bash
#!/bin/bash
# scripts/manage-cache.sh - Build cache management

set -euo pipefail

case "$1" in
    clean)
        echo "Cleaning build caches..."
        cargo clean
        rm -rf ~/.cargo/registry/cache
        rm -rf ~/.cargo/git/checkouts
        ;;
        
    optimize)
        echo "Optimizing build caches..."
        # Remove old artifacts
        find target -type f -atime +7 -delete
        # Compress debug info
        find target -name "*.rlib" -exec strip --strip-debug {} \;
        ;;
        
    stats)
        echo "Build cache statistics:"
        du -sh target/ || echo "No target directory"
        du -sh ~/.cargo/registry/ || echo "No registry cache"
        du -sh ~/.cargo/git/ || echo "No git cache"
        ;;
        
    warm)
        echo "Warming build caches..."
        cargo build --all-features
        cargo build --features tier_1
        cargo build --features tier_2
        cargo build --features tier_3
        ;;
        
    *)
        echo "Usage: $0 {clean|optimize|stats|warm}"
        exit 1
        ;;
esac
```

---

## 10. Integration with Configuration Management

### 10.1 Build-Time Configuration Validation

```rust
// build.rs - Configuration validation at build time

fn validate_configuration() -> Result<(), Box<dyn std::error::Error>> {
    // Load and validate all configuration files at build time
    let config_files = glob::glob("config-templates/*.toml")?;
    
    for config_path in config_files {
        let config_path = config_path?;
        let content = std::fs::read_to_string(&config_path)?;
        
        // Parse and validate TOML
        let _: toml::Value = toml::from_str(&content)
            .map_err(|e| format!("Invalid TOML in {}: {}", config_path.display(), e))?;
        
        println!("cargo:rerun-if-changed={}", config_path.display());
    }
    
    Ok(())
}
```

### 10.2 Environment-Specific Build Configuration

```rust
// src/build/env_config.rs - Environment-specific build configuration

use std::env;

pub fn configure_for_environment() {
    let tier = env::var("MISTER_SMITH_ENVIRONMENT_TIER")
        .unwrap_or_else(|_| "tier_2".to_string());
    
    match tier.as_str() {
        "tier_1" => {
            println!("cargo:rustc-cfg=tier=\"1\"");
            println!("cargo:rustc-env=DEFAULT_LOG_LEVEL=debug");
        }
        "tier_2" => {
            println!("cargo:rustc-cfg=tier=\"2\"");
            println!("cargo:rustc-env=DEFAULT_LOG_LEVEL=info");
        }
        "tier_3" => {
            println!("cargo:rustc-cfg=tier=\"3\"");
            println!("cargo:rustc-env=DEFAULT_LOG_LEVEL=warn");
            println!("cargo:rustc-cfg=feature=\"production\"");
        }
        _ => panic!("Invalid environment tier: {}", tier),
    }
}
```

---

## 11. Summary and Best Practices

### 11.1 Key Build Principles

1. **Reproducible Builds**: All builds must be deterministic and reproducible
2. **Feature Modularity**: Use feature flags to control binary size and functionality
3. **Cross-Platform Support**: Maintain build compatibility across all target platforms
4. **Performance Optimization**: Apply aggressive optimizations for production builds
5. **Security First**: Integrate security scanning into the build pipeline

### 11.2 Build Checklist

- [ ] All dependencies pinned to exact versions
- [ ] Feature flags properly configured for target environment
- [ ] Cross-compilation tested for all supported platforms
- [ ] Build scripts generate necessary code and assets
- [ ] CI/CD pipeline covers all build scenarios
- [ ] Release builds properly optimized and stripped
- [ ] Documentation generated and validated
- [ ] Security audit passes without warnings

### 11.3 Continuous Improvement

1. **Performance Monitoring**: Track build times and optimize bottlenecks
2. **Dependency Updates**: Regular updates with thorough testing
3. **Build Cache Optimization**: Maintain efficient caching strategies
4. **Pipeline Enhancement**: Continuously improve CI/CD efficiency
5. **Developer Experience**: Streamline local development workflows

---

## 12. Build Automation Scripts

### 12.1 Comprehensive Build Script Collection

The following automation scripts are provided for complete build and deployment workflows:

#### Environment Setup Script
- **Location**: `scripts/setup-build-env.sh`
- **Purpose**: Complete development environment setup with all dependencies
- **Features**:
  - Rust toolchain installation with required version (1.75)
  - Cross-compilation targets setup
  - Build tool installation (cargo extensions, cross, sccache)
  - Platform-specific optimizations (mold linker, system dependencies)
  - Git hooks configuration for quality gates
  - Development configuration templates

#### Automated Release Script  
- **Location**: `scripts/automated-release.sh`
- **Purpose**: End-to-end release process automation
- **Features**:
  - Version bumping (major, minor, patch, prerelease)
  - Changelog generation from git commits
  - Multi-platform artifact building
  - Security checks and quality gates
  - GitHub release creation with assets
  - crates.io publishing
  - Docker image builds and publishing

#### Cross-Compilation Script
- **Location**: `scripts/cross-compile-all.sh`
- **Purpose**: Build for all supported platforms in parallel
- **Features**:
  - 11 target platforms (Linux, macOS, Windows, ARM, WASM)
  - Parallel builds with configurable job count
  - Automatic archive creation and checksums
  - Build matrix reporting
  - Size optimization and stripping
  - Platform-specific configurations

#### Docker Build Script
- **Location**: `scripts/docker-build.sh`
- **Purpose**: Multi-architecture Docker image builds
- **Features**:
  - 4 image variants (minimal, standard, full, debug)
  - Multi-arch builds (amd64, arm64)
  - Layer caching optimization
  - Security scanning integration
  - Compose file generation
  - Registry publishing with tags

#### Performance Testing Script
- **Location**: `scripts/performance-testing.sh`
- **Purpose**: Comprehensive performance validation
- **Features**:
  - Micro-benchmarks with Criterion
  - Load testing with wrk and hyperfine
  - Stress testing to find breaking points
  - Memory profiling with Valgrind
  - CPU profiling with flame graphs
  - Automated performance reporting

#### Enhanced CI/CD Pipeline
- **Location**: `scripts/ci-cd-pipeline.yml`
- **Purpose**: Production-ready GitHub Actions workflow
- **Features**:
  - Security scanning (Trivy, dependency audit)
  - Multi-platform testing matrix
  - Performance regression detection
  - Docker multi-arch builds
  - Automated release management
  - Notification integrations

### 12.2 Script Usage Examples

```bash
# Complete environment setup
./scripts/setup-build-env.sh

# Development build with live reload
cargo watch -x "build --features dev"

# Cross-compile for all platforms
./scripts/cross-compile-all.sh --clean --features tier_3

# Create release build
./scripts/automated-release.sh minor

# Build Docker images
./scripts/docker-build.sh --all-variants --push

# Run performance tests
./scripts/performance-testing.sh --all --duration 300
```

### 12.3 Build Integration with MS Framework

These build scripts are specifically designed for the MS Framework and include:

1. **Feature Flag Validation**: Ensures compatible feature combinations
2. **Claude CLI Integration**: Special builds with Claude CLI hooks
3. **Tier-Based Configurations**: Optimized builds for each deployment tier
4. **Agent-Specific Optimizations**: Performance tuning for AI agent workloads
5. **Security Hardening**: Automated security scans and compliance checks

---

This comprehensive build specification provides all necessary information for autonomous agents to build and deploy the Mister Smith Framework across multiple platforms and configurations without requiring manual intervention or decision-making. The included automation scripts ensure reproducible, secure, and optimized builds for all deployment scenarios.